import { reactive, ref } from "vue";
import { useConvertKeyToCamel, useConvertKeyToSnake } from "@/utils/helpers";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";
import type { BotFilterProps } from "@/views/bot/utils/type";
import {
  getBots,
  getBotById,
  destroyBotById,
  bulkDestroyBots,
  createBot,
  updateBotById
} from "@/views/bot/utils/auth-api";
import { useLanguageStoreHook } from "@/store/modules/language";

export function useBotHook() {
  // Data/State
  const loading = ref(false);
  const pagination = reactive({
    total: 0,
    pageSize: 20,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100, 200],
    background: true,
    layout: "total, sizes, prev, pager, next, jumper"
  });
  const multipleSelection = ref([]);
  const records = ref([]);
  const sort = ref({ sortBy: "createdAt", sortOrder: "asc" });
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref<FieldValues>({
    model: null,
    name: null,
    language: useLanguageStoreHook().locale,
    description: null,
    systemPrompt: null,
    greetingMessage: $t("Hello! How can I help you?"),
    starterMessages: [""],
    logoUrl: null as string | null,
    logo: null,
    knowledge: {
      enabled: true,
      newUploads: [],
      libraryFiles: [],
      text: null
    },
    status: "active"
  });

  const botFormRef = ref();
  const filterRef = ref<BotFilterProps>({});

  // API Handlers
  const fnGetBots = async () => {
    loading.value = true;
    try {
      const response = await getBots(
        useConvertKeyToSnake({
          ...filterRef.value,
          order: `${sort.value.sortBy}:${sort.value.sortOrder}`,
          page: pagination.currentPage,
          limit: pagination.pageSize
        })
      );

      records.value = useConvertKeyToCamel(response.data);
      pagination.total = response.total || response.data?.length || 0;

      console.log("Bot records***************>", records.value);
      console.log("Pagination total***************>", pagination.total);
    } catch (e) {
      console.error("Get Bot error:", e);
      message(e.response?.data?.message || e?.message || $t("Get failed"), {
        type: "error"
      });
    } finally {
      loading.value = false;
    }
  };

  // Table Event Handlers
  const fnHandleSelectionChange = (val: any) => {
    multipleSelection.value = val;
  };

  const fnHandleSortChange = async ({ prop, order }) => {
    sort.value.sortBy = prop;
    sort.value.sortOrder = order === "ascending" ? "asc" : "desc";
    await fnGetBots();
  };

  const fnHandlePageChange = async (val: number) => {
    pagination.currentPage = val;
    await fnGetBots();
  };

  const fnHandleSizeChange = async (val: number) => {
    pagination.pageSize = val;
    pagination.currentPage = 1;
    await fnGetBots();
  };

  /*
   ***************************
   *   Delete handlers and actions
   ***************************
   */
  const handleDelete = async (id: number) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await fnHandleDelete(id);
    } catch {
      console.log("Delete cancelled");
    }
  };
  const fnHandleDelete = async (id: number) => {
    try {
      loading.value = true;
      const response = await destroyBotById(id);
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetBots();
        return true;
      }
      message(response.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Delete failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  // Bot chỉ có hard delete, không có soft delete và restore

  const handleBulkDelete = async (tableRef?: any) => {
    const ids = multipleSelection.value.map(item => item.id);
    if (ids.length === 0) {
      message($t("Please select items to delete"), {
        type: "warning"
      });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to delete selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const success = await fnHandleBulkDelete(ids);
      if (success && tableRef?.clearSelection) {
        tableRef.clearSelection();
      }
    } catch {
      console.log("Delete cancelled");
    }
  };
  const fnHandleBulkDelete = async (ids: number[]) => {
    try {
      loading.value = true;
      const response = await bulkDestroyBots({ ids });
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetBots();
        return true;
      }
      message(response.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      console.error("Bulk delete Bots error:", error);
      message(error.response?.data?.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  /*
   ***************************
   *   CTA handlers
   ***************************
   */
  const handleSubmit = async (values: FieldValues) => {
    if (values.id != null) {
      await fnHandleUpdateBot(Number(values.id), values);
      return;
    }
    const success = await fnHandleCreateBot(values);
    if (success) {
      drawerValues.value = { status: "active" };
      handleReset();
    }
  };
  const fnHandleCreateBot = async (formData: FieldValues) => {
    try {
      loading.value = true;
      const response = await createBot(formData);
      if (response.success) {
        message(response.message || $t("Create successful"), {
          type: "success"
        });
        await fnGetBots();
        return true;
      }
      message(response?.message || $t("Create failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Create failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };
  const fnHandleUpdateBot = async (id: number, formData: FieldValues) => {
    try {
      loading.value = true;
      const response = await updateBotById(id, formData);
      if (response.success) {
        message(response.message || $t("Update successful"), {
          type: "success"
        });
        await fnGetBots();
        return true;
      }
      message(response?.message || $t("Update failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Update failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  const handleFilter = async (values: BotFilterProps) => {
    filterRef.value = values;
    await fnGetBots();
  };

  const handleReset = () => {
    // Reset drawerValues to default
    drawerValues.value = {
      model: null,
      name: null,
      language: useLanguageStoreHook().locale,
      description: null,
      systemPrompt: null,
      greetingMessage: $t("Hello! How can I help you?"),
      starterMessages: [""],
      logoUrl: null as string | null,
      logo: null,
      knowledge: {
        enabled: true,
        newUploads: [],
        libraryFiles: [],
        text: null
      },
      status: "active"
    };

    console.log("botFormRef?.value------->");

    // Reset form if exists - el-form uses resetFields() method
    if (botFormRef?.value) {
      botFormRef.value?.resetFields();
    }
  };

  // Handle edit by fetching full data from API
  const handleEdit = async (uuid: string) => {
    try {
      loading.value = true;
      const response = await getBotById(uuid);

      if (response.success && response.data) {
        const botData = useConvertKeyToCamel(response.data) as any;

        // Map data structure for form
        const formData = {
          ...botData,
          // Map aiModel to model field for form
          model: botData.aiModel?.key || botData.aiModelId || null,
          // Map logo to logoUrl for form
          logoUrl: botData.logo || botData.logoUrl || null,
          // Ensure starterMessages is an array
          starterMessages: Array.isArray(botData.starterMessages)
            ? botData.starterMessages
            : [""],
          // Ensure knowledge object exists
          knowledge: botData.knowledge || {
            enabled: true,
            newUploads: [],
            libraryFiles: [],
            text: null
          },
          // Set default language if not present
          language: botData.language || useLanguageStoreHook().locale
        };

        drawerValues.value = formData;
        drawerVisible.value = true;

        console.log("Bot data loaded for edit:", formData);
      } else {
        message(response.message || $t("Failed to load bot data"), {
          type: "error"
        });
      }
    } catch (error) {
      console.error("Error loading bot for edit:", error);
      message(
        error.response?.data?.message ||
        error?.message ||
        $t("Failed to load bot data"),
        { type: "error" }
      );
    } finally {
      loading.value = false;
    }
  };

  return {
    // Data/State
    loading,
    filterRef,
    pagination,
    records,
    multipleSelection,
    sort,
    filterVisible,
    drawerVisible,
    drawerValues,
    botFormRef,

    // API Handlers
    fnGetBots,
    fnHandleCreateBot,
    fnHandleUpdateBot,
    fnHandleDelete,
    fnHandleBulkDelete,

    // Table Event Handlers
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandlePageChange,
    fnHandleSizeChange,

    // UI Action Handlers
    handleDelete,
    handleBulkDelete,

    // Form Handlers
    handleSubmit,
    handleReset,
    handleFilter,
    handleEdit
  };
}
