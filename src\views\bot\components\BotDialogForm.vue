<script setup lang="ts">
import { ElMessage, ElMessageBox, UploadFile, UploadFiles } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import { reactive, ref, onMounted, watch, onBeforeUnmount } from "vue";
import { useLanguageStoreHook } from "@/store/modules/language";
import {
  Plus,
  Refresh,
  Promotion,
  Delete,
  UploadFilled
} from "@element-plus/icons-vue";
import { $t } from "@/plugins/i18n";

import { message } from "@/utils/message";
import { useRoute } from "vue-router";
import { getToken } from "@/utils/auth";
import { PlusDialog } from "plus-pro-components";
import { useBotHook } from "@/views/bot/utils/hook";
import {
  getAiModelDropdown,
  getGeneralPrompts,
  getRemoveFile
} from "@/views/bot/utils/auth-api";
import { botRules } from "../utils/rule";

// --- State Management ---
interface Props {
  useBot: any;
}

const props = defineProps<{
  useBot: any;
}>();

const route = useRoute();
const formRef = ref<FormInstance>();
const aiModels = ref([]);
const knowledgeTab = ref("files");
const knowledgeFiles = ref([]);

const loading = reactive({
  prompt: false,
  greeting: false,
  starters: false,
  submit: false
});

const fileLibrary = ref([]);
const originalFileLibrary = ref([]);
const fileFilter = reactive({
  name: "",
  dateFrom: "",
  dateTo: ""
});

const { drawerValues, handleSubmit, drawerVisible, botFormRef, handleReset } =
  props.useBot;

// --- API Call Logic ---
const callGeneralPrompts = async (params: object) => {
  try {
    const { data, success } = await getGeneralPrompts(params);

    if (success) {
      return data;
    }
    return null;
  } catch (error) {
    return null;
  }
};

// --- Component Methods ---
const handleAvatarChange = (uploadFile: any) => {
  drawerValues.value.logoUrl = URL.createObjectURL(uploadFile.raw);
  drawerValues.value.logo = uploadFile.raw;
};

const addStarter = () => {
  if (!drawerValues.value.starterMessages) {
    drawerValues.value.starterMessages = [];
  }
  (drawerValues.value.starterMessages as string[]).push("");
};

const removeStarter = (index: number) => {
  if (drawerValues.value.starterMessages) {
    (drawerValues.value.starterMessages as string[]).splice(index, 1);
  }
};

/*:::::::::::::::::::::: Handle Event File ------------------------------------------*/
const handleLibrarySelectionChange = (selection: any[]) => {
  (drawerValues.value.knowledge as any).libraryFiles = selection;
};

// Filter files based on name and date
const filterFiles = () => {
  let filtered = [...originalFileLibrary.value];

  // Filter by name
  if (fileFilter.name.trim()) {
    filtered = filtered.filter((file: any) =>
      file.name.toLowerCase().includes(fileFilter.name.toLowerCase())
    );
  }

  // Filter by date range
  if (fileFilter.dateFrom) {
    filtered = filtered.filter((file: any) => {
      const fileDate = new Date(file.createdAt || file.uploadedAt);
      const fromDate = new Date(fileFilter.dateFrom);
      return fileDate >= fromDate;
    });
  }

  if (fileFilter.dateTo) {
    filtered = filtered.filter((file: any) => {
      const fileDate = new Date(file.createdAt || file.uploadedAt);
      const toDate = new Date(fileFilter.dateTo);
      toDate.setHours(23, 59, 59, 999); // End of day
      return fileDate <= toDate;
    });
  }

  fileLibrary.value = filtered;
};

// Clear all filters
const clearFilters = () => {
  fileFilter.name = "";
  fileFilter.dateFrom = "";
  fileFilter.dateTo = "";
  filterFiles();
};

const handleUploadSuccess = (response: any, uploadFile: UploadFile) => {
  if (drawerValues.value.knowledge) {
    drawerValues.value.knowledge.newUploads?.push({
      ...response.data,
      uid: uploadFile.uid
    });
  }
};

const beforeRemove = async (
  uploadFile: UploadFile,
  uploadFiles: UploadFiles
) => {
  const files = drawerValues.value.knowledge.newUploads;
  const index = files.findIndex((file: any) => {
    return file.uid === uploadFile.uid;
  });

  if (index !== -1) {
    try {
      const { success } = await getRemoveFile(files[index]);
      if (success) {
        files.splice(index, 1);
        return true;
      }
    } catch (e) {
      return false;
    }
  }
  return false;
};

const handleFileChange = (uploadFile: UploadFile, uploadFiles: UploadFiles) => {
  const isDuplicate =
    uploadFiles.filter(
      (file: any) =>
        file.name === uploadFile.name && file.size === uploadFile.size
    ).length > 1;

  if (isDuplicate) {
    ElMessage.warning(
      $t('File "{fileName}" is already in the list.', {
        fileName: uploadFile.name
      })
    );
    if ((drawerValues.value.knowledge as any)?.newUploads) {
      (drawerValues.value.knowledge as any).newUploads.pop();
    }
  }
};

/*:::::::::::::::: General Prompt -----------------------------------------*/
const generatePrompt = async () => {
  try {
    const { value } = await ElMessageBox.prompt(
      $t("Briefly describe the role of the Agent:"),
      $t("✨ Prompt Generator Assistant"),
      {
        confirmButtonText: $t("Generate"),
        cancelButtonText: $t("Cancel"),
        inputPlaceholder: $t(
          "Example: Vietnamese literature lesson planning assistant"
        )
      }
    );

    if (!value) return;

    loading.prompt = true;
    const result = await callGeneralPrompts({
      type: "system_prompt",
      role: value
    });

    if (result) {
      drawerValues.value.systemPrompt = result.trim();
      message($t("Prompt generated successfully!"), { type: "success" });
      await formRef.value?.validateField("systemPrompt");
    }
  } catch (e) {
    if (e !== "cancel") {
      console.error("Error generating prompt:", e);
    }
  } finally {
    loading.prompt = false;
  }
};

const generateGreeting = async () => {
  if (!drawerValues.value.name) {
    message($t("Please enter AI Assistant Name first."), { type: "warning" });
    return;
  }

  loading.greeting = true;

  try {
    const result = await callGeneralPrompts({
      type: "greeting_message",
      name: drawerValues.value.name
    });
    if (result) {
      drawerValues.value.greetingMessage = result.replace(/"/g, "").trim();
    }
  } catch (e) {
    console.error("Error generating greeting:", e);
  } finally {
    loading.greeting = false;
  }
};

const generateStarters = async () => {
  if (!drawerValues.value.systemPrompt) {
    message($t("Please create System Prompt for best suggestions."), {
      type: "warning"
    });
    return;
  }

  loading.starters = true;
  try {
    const result = await callGeneralPrompts({
      type: "starting_message",
      system_prompt: drawerValues.value.systemPrompt
    });
    if (result) {
      try {
        const cleanedResult = result.match(/\[.*\]/s)?.[0] || result;
        const starters = JSON.parse(cleanedResult);
        if (
          Array.isArray(starters) &&
          starters.every(s => typeof s === "string")
        ) {
          drawerValues.value.starterMessages = starters.slice(0, 4);
        } else {
          message($t("AI returned data not in string array format."), {
            type: "error"
          });
        }
      } catch (e) {
        console.error("JSON parsing error:", e, "Raw result:", result);
        message($t("Cannot parse suggestions from AI."), { type: "error" });
      }
    }
  } catch (e) {
    console.error("Error generating starters:", e);
  } finally {
    loading.starters = false;
  }
};

const handleGetAiModelDropdown = async () => {
  try {
    const { data, success } = await getAiModelDropdown();
    if (success) {
      aiModels.value = data.map((model: any) => ({
        value: model.value,
        label: `${model.label} (${model.value})`
      }));
    }
  } catch (error) {
    console.error("Error loading AI models:", error);
  }
};

// Load file library from API
const loadFileLibrary = async () => {
  try {
    // Mock data for now - replace with actual API call
    const mockFiles = [
      {
        id: 1,
        name: "User Manual.pdf",
        type: "PDF",
        size: "2.5 MB",
        createdAt: "2024-01-15T10:30:00Z",
        date: "2024-01-15"
      },
      {
        id: 2,
        name: "FAQ Document.docx",
        type: "DOCX",
        size: "1.2 MB",
        createdAt: "2024-01-20T14:15:00Z",
        date: "2024-01-20"
      },
      {
        id: 3,
        name: "Product Catalog.pdf",
        type: "PDF",
        size: "5.8 MB",
        createdAt: "2024-02-01T09:45:00Z",
        date: "2024-02-01"
      },
      {
        id: 4,
        name: "Training Guide.pdf",
        type: "PDF",
        size: "3.2 MB",
        createdAt: "2024-02-15T16:20:00Z",
        date: "2024-02-15"
      },
      {
        id: 5,
        name: "API Documentation.md",
        type: "MD",
        size: "800 KB",
        createdAt: "2024-03-01T11:10:00Z",
        date: "2024-03-01"
      },
      {
        id: 6,
        name: "Company Policy.docx",
        type: "DOCX",
        size: "1.5 MB",
        createdAt: "2024-03-10T13:45:00Z",
        date: "2024-03-10"
      }
    ];

    originalFileLibrary.value = mockFiles;
    fileLibrary.value = [...mockFiles];
  } catch (error) {
    console.error("Error loading file library:", error);
  }
};

// Watch for filter changes
watch(
  fileFilter,
  () => {
    filterFiles();
  },
  { deep: true }
);

onMounted(async () => {
  await handleGetAiModelDropdown();
  await loadFileLibrary();
});

onBeforeUnmount(() => {
  console.log("onBeforeUnmount----->:::", botFormRef.value);
  handleReset();
});
</script>

<template>
  <div class="">
    <div class="main-grid">
      <!-- Left Column -->
      <div class="left-column space-y-6">
        <div class="card">
          <h2 class="section-title !block text-center">{{ $t("Avatar") }}</h2>
          <div class="flex justify-center">
            <el-upload class="avatar-uploader" action="#" :show-file-list="false" :auto-upload="false"
              @change="handleAvatarChange">
              <img v-if="drawerValues.logoUrl" :src="drawerValues.logoUrl as string" class="avatar" alt="avatar" />
              <el-icon v-else class="avatar-uploader-icon">
                <Plus />
              </el-icon>
            </el-upload>
          </div>
          <p class="text-xs text-center text-gray-500 !mt-4">
            {{ $t("Upload JPG, PNG, JPEG images. Size under 5MB.") }}
          </p>
        </div>

        <div class="card">
          <h2 class="section-title">{{ $t("Advanced Settings") }}</h2>
          <el-form-item :label="$t('Use Knowledge Base')">
            <el-switch v-model="drawerValues.knowledge.enabled" size="large" />
          </el-form-item>
          <p class="text-xs text-gray-500">
            {{
              $t(
                "Enable this feature to allow Agent access to your private knowledge sources."
              )
            }}
          </p>
        </div>
      </div>

      <!-- Right Column -->
      <div class="right-column space-y-6">
        <el-form ref="botFormRef" :model="drawerValues" :rules="botRules" label-position="top"
          require-asterisk-position="right" size="default" class="flex flex-col gap-2">
          <div class="card">
            <h2 class="section-title">{{ $t("Basic Information") }}</h2>
            <el-row :gutter="20">
              <el-col :xs="24" :sm="8">
                <el-form-item :label="$t('AI Assistant Name')" prop="name">
                  <el-input v-model="drawerValues.name" clearable :placeholder="$t('Example: Administrative Procedure Consultant')
                    " />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="8">
                <el-form-item :label="$t('AI Model')" prop="model">
                  <el-select v-model="drawerValues.model" class="w-full">
                    <el-option v-for="model in aiModels" :key="model.value" :label="model.label" :value="model.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="8">
                <el-form-item :label="$t('Language')">
                  <el-select v-model="drawerValues.language" class="w-full">
                    <el-option :label="$t('Vietnamese')" value="vi" />
                    <el-option :label="$t('English')" value="en" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item :label="$t('Description')">
              <el-input v-model="drawerValues.description" type="textarea" :placeholder="$t('Brief description of AI Assistant functions.')
                " />
            </el-form-item>
          </div>

          <div class="card">
            <div class="flex flex-row items-center justify-between">
              <h2 class="section-title">{{ $t("AI Brain") }}</h2>
              <el-button class="gemini-button" :loading="loading.prompt" round @click="generatePrompt">
                {{ $t("✨ Prompt Generator Assistant") }}
              </el-button>
            </div>
            <el-form-item :label="$t('Suggestion - Prompt')" prop="systemPrompt">
              <el-input v-model="drawerValues.systemPrompt" type="textarea" :rows="8"
                :placeholder="$t('This is the most important part...')" />
            </el-form-item>
          </div>

          <div class="card">
            <h2 class="section-title">{{ $t("Chat Interface") }}</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div class="space-y-6">
                <el-form-item :label="$t('Welcome Message')">
                  <el-input v-model="drawerValues.greetingMessage"
                    :placeholder="$t('Example: Hello! How can I help you?')" type="textarea" :rows="3">
                    <template #append>
                      <el-button class="gemini-button" :loading="loading.greeting" @click="generateGreeting">
                        {{ $t("✨ Generate") }}
                      </el-button>
                    </template>
                  </el-input>
                </el-form-item>

                <el-form-item>
                  <template #label>
                    <div class="flex justify-between w-full items-center">
                      <span>{{ $t("Starter Suggestions") }}</span>
                      <el-button class="gemini-button" :loading="loading.starters" round size="small"
                        @click="generateStarters">
                        {{ $t("✨ Generate") }}
                      </el-button>
                    </div>
                  </template>
                  <div v-for="(starter, index) in drawerValues.starterMessages" :key="index"
                    class="flex items-center mb-2 w-full">
                    <el-input v-model="drawerValues.starterMessages[index]"
                      :placeholder="$t('Example: What is the tuition fee?')" class="flex-1 mr-1" />
                    <el-button type="danger" :icon="Delete" circle plain size="small" @click="removeStarter(index)" />
                  </div>
                  <el-button :icon="Plus" size="small" @click="addStarter">
                    {{ $t("Add Suggestion") }}
                  </el-button>
                </el-form-item>
              </div>

              <div>
                <h3 class="text-sm font-medium text-gray-500 mb-2 text-center">
                  {{ $t("Preview") }}
                </h3>
                <div class="chat-preview-container">
                  <div class="chat-preview-window">
                    <div class="chat-preview-header">
                      <img :src="drawerValues.logoUrl ||
                        'https://placehold.co/40x40/E2E8F0/4A5568?text=AI'
                        " class="chat-preview-avatar" alt="logo" />
                      <span class="font-semibold text-gray-700">
                        {{ drawerValues.name || $t("AI Assistant") }}
                      </span>
                    </div>
                    <div class="chat-preview-body">
                      <div class="chat-bubble">
                        <span>{{ drawerValues.greetingMessage }}</span>
                      </div>
                      <div v-if="drawerValues.starterMessages?.some(s => s)" class="starter-pills-container">
                        <div v-for="starter in drawerValues.starterMessages?.filter(
                          s => s
                        )" :key="starter" class="starter-pill">
                          {{ starter }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-if="drawerValues.knowledge.enabled" class="card">
            <h2 class="section-title">{{ $t("Knowledge Base") }}</h2>
            <el-tabs v-model="knowledgeTab" type="border-card">
              <el-tab-pane :label="$t('Upload New Files')" name="files">
                <el-upload v-model:file-list="knowledgeFiles" class="w-full" drag
                  action="/api/auth/knowledge-bases/files/upload" multiple :auto-upload="true" :headers="{
                    Authorization: `Bearer ${getToken().accessToken ?? getToken()}`,
                    'X-Requested-With': 'XMLHttpRequest'
                  }" :before-remove="beforeRemove" @change="handleFileChange" @success="handleUploadSuccess">
                  <el-icon class="el-icon--upload">
                    <upload-filled />
                  </el-icon>
                  <div class="el-upload__text">
                    {{ $t("Drag files here or") }}
                    <em>{{ $t("click to upload") }}</em>
                  </div>
                </el-upload>
              </el-tab-pane>

              <el-tab-pane :label="$t('Text')" name="text">
                <el-input v-model="drawerValues.knowledge.text" type="textarea" :rows="10"
                  :placeholder="$t('Paste text content here.')" />
              </el-tab-pane>

              <el-tab-pane :label="$t('Choose from Library')" name="library">
                <p class="text-sm text-gray-500 mb-4">
                  {{
                    $t(
                      "Select previously uploaded documents to add to the knowledge for this Agent."
                    )
                  }}
                </p>

                <!-- Filter Controls -->
                <div class="mb-4 p-3 bg-gray-50 rounded-lg">
                  <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">
                        {{ $t("File Name") }}
                      </label>
                      <el-input v-model="fileFilter.name" :placeholder="$t('Search by file name...')" clearable
                        size="small" />
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">
                        {{ $t("From Date") }}
                      </label>
                      <el-date-picker v-model="fileFilter.dateFrom" type="date" :placeholder="$t('Select start date')"
                        size="small" style="width: 100%" />
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">
                        {{ $t("To Date") }}
                      </label>
                      <el-date-picker v-model="fileFilter.dateTo" type="date" :placeholder="$t('Select end date')"
                        size="small" style="width: 100%" />
                    </div>
                  </div>
                  <div class="flex justify-end mt-3">
                    <el-button size="small" @click="clearFilters">
                      {{ $t("Clear Filters") }}
                    </el-button>
                  </div>
                </div>

                <el-table :data="fileLibrary" height="250" @selection-change="handleLibrarySelectionChange">
                  <el-table-column type="selection" width="55" />
                  <el-table-column prop="name" :label="$t('File Name')" />
                  <el-table-column prop="type" :label="$t('Type')" width="100" />
                  <el-table-column prop="date" :label="$t('Upload Date')" width="180" />
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </div>
          <div class="flex justify-end gap-2">
            <el-button round type="danger" :icon="Promotion" size="large" :loading="loading.submit"
              @click="handleSubmit(drawerValues)">
              {{ $t("Save") }}
            </el-button>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.main-grid {
  display: grid;
  grid-template-columns: 320px 1fr;
  gap: 2rem;
}

.left-column {
  position: sticky;
  top: 2rem;
  height: calc(100vh - 4rem);
}

.card {
  background-color: #ffffff;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow:
    0 4px 6px -1px rgb(0 0 0 / 0.05),
    0 2px 4px -2px rgb(0 0 0 / 0.05);
  border: 1px solid #e2e8f0;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1.25rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.avatar-uploader .el-upload {
  border: 2px dashed #d9d9d9;
  border-radius: 50%;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
  width: 150px;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}

.avatar {
  width: 150px;
  height: 150px;
  display: block;
  object-fit: cover;
}

.gemini-button {
  background: linear-gradient(to right, #8e44ad, #3498db);
  color: white;
  border: none;
}

.gemini-button:hover {
  opacity: 0.9;
}

.chat-preview-container {
  background-color: #f1f5f9;
  border-radius: 0.75rem;
  padding: 1rem;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-preview-window {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.chat-preview-header {
  padding: 0.75rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
}

.chat-preview-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 0.75rem;
}

.chat-preview-body {
  padding: 1rem;
  flex-grow: 1;
  font-size: 0.875rem;
}

.chat-bubble {
  background-color: #e2e8f0;
  color: #334155;
  padding: 0.5rem 0.75rem;
  border-radius: 1rem;
  border-bottom-left-radius: 0.25rem;
  max-width: 90%;
  display: inline-block;
  word-wrap: break-word;
}

.starter-pills-container {
  margin-top: 1rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.starter-pill {
  background-color: transparent;
  border: 1px solid #cbd5e1;
  color: #475569;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  cursor: pointer;
  transition: all 0.2s;
}

.starter-pill:hover {
  background-color: #e2e8f0;
  border-color: #94a3b8;
}

@media (max-width: 1024px) {
  .main-grid {
    grid-template-columns: 1fr;
  }

  .left-column {
    position: static;
    height: auto;
  }
}
</style>
